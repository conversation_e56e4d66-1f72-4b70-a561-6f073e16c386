export class PriceTrackerMarketAnalysisComponent {
  constructor(container) {
    this.container = container;
    this.priceData = [];
    this.companyAnalysis = {
      yearlyData: {},
      monthlyData: {},
      totalSpending: 0,
      totalPurchases: 0,
      activeVendors: new Set(),
      activeParts: new Set(),
      trends: {}
    };
    this.searchTerm = '';
    this.selectedView = 'yearly'; // yearly, monthly
    this.chartInstances = new Map();
  }

  async init(priceData, dateRange) {
    console.log('Initializing Company Market Analysis with', priceData.length, 'records');
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};

    // Load Chart.js library
    await this.loadChartLibrary();

    // Process data for company-wide analysis
    this.processCompanyAnalysis();

    // Render the analysis
    this.render();
  }

  async loadChartLibrary() {
    return new Promise((resolve, reject) => {
      if (window.ApexCharts) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '@kpi/library/apexcharts.min.js';
      script.onload = () => {
        console.log('ApexCharts loaded successfully');
        resolve();
      };
      script.onerror = () => {
        console.error('Failed to load ApexCharts');
        reject(new Error('Failed to load ApexCharts'));
      };
      document.head.appendChild(script);
    });
  }

  processCompanyAnalysis() {
    console.log('Processing company-wide market analysis...');

    // Reset analysis data
    this.companyAnalysis = {
      yearlyData: {},
      monthlyData: {},
      totalSpending: 0,
      totalPurchases: 0,
      activeVendors: new Set(),
      activeParts: new Set(),
      trends: {}
    };

    // Process all purchase data for company-wide analysis
    this.priceData.forEach(item => {
      const date = new Date(item.purchaseDate);
      const year = date.getFullYear();
      const month = date.getMonth() + 1; // 1-12
      const monthKey = `${year}-${String(month).padStart(2, '0')}`;
      const purchaseValue = (item.unitCost || 0) * (item.quantity || 1);

      // Track company totals
      this.companyAnalysis.totalSpending += purchaseValue;
      this.companyAnalysis.totalPurchases += 1;
      this.companyAnalysis.activeVendors.add(item.vendorName);
      this.companyAnalysis.activeParts.add(item.inventoryId);

      // Yearly aggregation
      if (!this.companyAnalysis.yearlyData[year]) {
        this.companyAnalysis.yearlyData[year] = {
          year: year,
          totalSpending: 0,
          totalPurchases: 0,
          vendors: new Set(),
          parts: new Set(),
          monthlyBreakdown: {}
        };
      }

      const yearData = this.companyAnalysis.yearlyData[year];
      yearData.totalSpending += purchaseValue;
      yearData.totalPurchases += 1;
      yearData.vendors.add(item.vendorName);
      yearData.parts.add(item.inventoryId);

      // Monthly breakdown within year
      if (!yearData.monthlyBreakdown[month]) {
        yearData.monthlyBreakdown[month] = {
          month: month,
          monthName: this.getMonthName(month),
          totalSpending: 0,
          totalPurchases: 0,
          vendors: new Set(),
          parts: new Set()
        };
      }

      yearData.monthlyBreakdown[month].totalSpending += purchaseValue;
      yearData.monthlyBreakdown[month].totalPurchases += 1;
      yearData.monthlyBreakdown[month].vendors.add(item.vendorName);
      yearData.monthlyBreakdown[month].parts.add(item.inventoryId);

      // Global monthly data
      if (!this.companyAnalysis.monthlyData[monthKey]) {
        this.companyAnalysis.monthlyData[monthKey] = {
          monthKey: monthKey,
          year: year,
          month: month,
          monthName: this.getMonthName(month),
          totalSpending: 0,
          totalPurchases: 0,
          vendors: new Set(),
          parts: new Set()
        };
      }

      this.companyAnalysis.monthlyData[monthKey].totalSpending += purchaseValue;
      this.companyAnalysis.monthlyData[monthKey].totalPurchases += 1;
      this.companyAnalysis.monthlyData[monthKey].vendors.add(item.vendorName);
      this.companyAnalysis.monthlyData[monthKey].parts.add(item.inventoryId);
    });

    // Calculate percentage changes and trends
    this.calculateTrends();

    // Convert Sets to counts for final data
    Object.values(this.companyAnalysis.yearlyData).forEach(yearData => {
      yearData.vendorCount = yearData.vendors.size;
      yearData.partCount = yearData.parts.size;

      Object.values(yearData.monthlyBreakdown).forEach(monthData => {
        monthData.vendorCount = monthData.vendors.size;
        monthData.partCount = monthData.parts.size;
      });
    });

    Object.values(this.companyAnalysis.monthlyData).forEach(monthData => {
      monthData.vendorCount = monthData.vendors.size;
      monthData.partCount = monthData.parts.size;
    });

    this.companyAnalysis.totalVendors = this.companyAnalysis.activeVendors.size;
    this.companyAnalysis.totalParts = this.companyAnalysis.activeParts.size;

    console.log('Company analysis processed:', {
      totalSpending: this.formatCurrency(this.companyAnalysis.totalSpending),
      totalPurchases: this.companyAnalysis.totalPurchases,
      totalVendors: this.companyAnalysis.totalVendors,
      totalParts: this.companyAnalysis.totalParts,
      years: Object.keys(this.companyAnalysis.yearlyData).length
    });
  }

  calculateTrends() {
    // Calculate year-over-year changes
    const years = Object.keys(this.companyAnalysis.yearlyData).map(y => parseInt(y)).sort();

    for (let i = 1; i < years.length; i++) {
      const currentYear = years[i];
      const previousYear = years[i - 1];

      const currentData = this.companyAnalysis.yearlyData[currentYear];
      const previousData = this.companyAnalysis.yearlyData[previousYear];

      if (previousData.totalSpending > 0) {
        currentData.spendingGrowth = ((currentData.totalSpending - previousData.totalSpending) / previousData.totalSpending) * 100;
      } else {
        currentData.spendingGrowth = 0;
      }

      if (previousData.totalPurchases > 0) {
        currentData.purchaseGrowth = ((currentData.totalPurchases - previousData.totalPurchases) / previousData.totalPurchases) * 100;
      } else {
        currentData.purchaseGrowth = 0;
      }
    }

    // Calculate month-over-month changes for current year
    const currentYear = Math.max(...years);
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (currentYearData) {
      const months = Object.keys(currentYearData.monthlyBreakdown).map(m => parseInt(m)).sort();

      for (let i = 1; i < months.length; i++) {
        const currentMonth = months[i];
        const previousMonth = months[i - 1];

        const currentMonthData = currentYearData.monthlyBreakdown[currentMonth];
        const previousMonthData = currentYearData.monthlyBreakdown[previousMonth];

        if (previousMonthData.totalSpending > 0) {
          currentMonthData.spendingGrowth = ((currentMonthData.totalSpending - previousMonthData.totalSpending) / previousMonthData.totalSpending) * 100;
        } else {
          currentMonthData.spendingGrowth = 0;
        }
      }
    }
  }

  calculateTariffImpact(currentMonthData, previousMonthData) {
    if (!currentMonthData || !previousMonthData || previousMonthData.totalSpending === 0) {
      return 0;
    }

    // Calculate average unit cost for both months
    const currentAvgCost = currentMonthData.totalSpending / currentMonthData.totalPurchases;
    const previousAvgCost = previousMonthData.totalSpending / previousMonthData.totalPurchases;

    // Estimate tariff impact as a portion of price increase
    // This is a simplified calculation - in reality, you'd need actual tariff data
    const priceIncrease = ((currentAvgCost - previousAvgCost) / previousAvgCost) * 100;

    // Assume tariff impact is roughly 60-80% of significant price increases
    // This is an estimation - adjust based on your actual tariff knowledge
    if (priceIncrease > 5) {
      return priceIncrease * 0.7; // 70% of increase attributed to tariffs
    } else if (priceIncrease > 2) {
      return priceIncrease * 0.5; // 50% of moderate increases
    } else {
      return Math.max(0, priceIncrease * 0.3); // 30% of small increases
    }
  }

  getMonthName(monthNumber) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthNumber - 1] || 'Unknown';
  }

  render() {
    this.container.innerHTML = `
      <div class="market-analysis-dashboard">
        <!-- View Toggle -->
        ${this.renderViewToggle()}

        <!-- Main Analysis Content -->
        <div id="analysis-content">
          ${this.selectedView === 'yearly' ? this.renderYearlyAnalysis() : this.renderMonthlyAnalysis()}
        </div>
      </div>
    `;

    this.setupEventListeners();

    // Render charts after DOM is ready
    setTimeout(() => {
      this.renderCharts();
    }, 100);
  }



  renderMonthlyOverview() {
    const currentYear = new Date().getFullYear();
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (!currentYearData) {
      return '';
    }

    // Get all 12 months, fill missing months with zero data
    const allMonths = [];
    for (let month = 1; month <= 12; month++) {
      const monthData = currentYearData.monthlyBreakdown[month];
      if (monthData) {
        // Calculate price increase and tariff impact
        const avgUnitCost = monthData.totalPurchases > 0 ? monthData.totalSpending / monthData.totalPurchases : 0;
        const previousMonth = month > 1 ? currentYearData.monthlyBreakdown[month - 1] : null;
        const previousAvgCost = previousMonth && previousMonth.totalPurchases > 0 ?
          previousMonth.totalSpending / previousMonth.totalPurchases : avgUnitCost;

        const priceIncrease = previousAvgCost > 0 ? ((avgUnitCost - previousAvgCost) / previousAvgCost) * 100 : 0;
        const tariffImpact = this.calculateTariffImpact(monthData, previousMonth);

        allMonths.push({
          month: month,
          monthName: this.getMonthName(month),
          totalSpending: monthData.totalSpending,
          priceIncrease: priceIncrease,
          tariffImpact: tariffImpact,
          totalPurchases: monthData.totalPurchases,
          vendorCount: monthData.vendorCount,
          partCount: monthData.partCount,
          avgUnitCost: avgUnitCost
        });
      } else {
        allMonths.push({
          month: month,
          monthName: this.getMonthName(month),
          totalSpending: 0,
          priceIncrease: 0,
          tariffImpact: 0,
          totalPurchases: 0,
          vendorCount: 0,
          partCount: 0,
          avgUnitCost: 0
        });
      }
    }

    return `
      <!-- Monthly Tariff Impact Overview -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Monthly Tariff Impact Analysis - ${currentYear}</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Price increases and tariff effects on purchasing costs by month</p>
        </div>

        <!-- Monthly Cards Grid -->
        <div class="p-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            ${allMonths.map(monthData => {
              const priceIncreaseClass = monthData.priceIncrease >= 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400';
              const priceIncreaseIcon = monthData.priceIncrease >= 0 ? '📈' : '📉';
              const tariffImpactClass = monthData.tariffImpact >= 5 ? 'text-red-600 dark:text-red-400' :
                                       monthData.tariffImpact >= 2 ? 'text-yellow-600 dark:text-yellow-400' :
                                       'text-green-600 dark:text-green-400';
              const hasData = monthData.totalSpending > 0;

              return `
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 ${hasData ? 'border-l-4 border-orange-500' : 'opacity-60'}">
                  <div class="flex justify-between items-start mb-2">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">${monthData.monthName}</h4>
                    ${hasData && monthData.priceIncrease !== 0 ? `
                      <span class="text-xs font-medium ${priceIncreaseClass}">
                        ${priceIncreaseIcon} ${Math.abs(monthData.priceIncrease).toFixed(1)}%
                      </span>
                    ` : ''}
                  </div>

                  <div class="space-y-1">
                    <div class="text-lg font-bold text-gray-900 dark:text-white">
                      ${hasData ? this.formatCurrency(monthData.totalSpending) : 'No Data'}
                    </div>

                    ${hasData ? `
                      <div class="text-xs font-medium ${tariffImpactClass}">
                        Tariff Impact: ${monthData.tariffImpact.toFixed(1)}%
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        Avg Unit Cost: ${this.formatCurrency(monthData.avgUnitCost)}
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        ${monthData.totalPurchases} purchases
                      </div>
                      <div class="text-xs text-gray-600 dark:text-gray-400">
                        ${monthData.vendorCount} vendors, ${monthData.partCount} parts
                      </div>
                    ` : `
                      <div class="text-xs text-gray-500 dark:text-gray-500">
                        No purchases this month
                      </div>
                    `}
                  </div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      </div>
    `;
  }

  renderViewToggle() {
    return `
      <div class="flex justify-center mb-6">
        <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button id="yearly-view" class="px-4 py-2 text-sm font-medium rounded-md transition-colors ${this.selectedView === 'yearly' ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'}">
            Yearly Analysis
          </button>
          <button id="monthly-view" class="px-4 py-2 text-sm font-medium rounded-md transition-colors ${this.selectedView === 'monthly' ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'}">
            Monthly Analysis
          </button>
        </div>
      </div>
    `;
  }

  renderYearlyAnalysis() {
    const years = Object.keys(this.companyAnalysis.yearlyData).map(y => parseInt(y)).sort();

    return `
      <div class="space-y-8">
        <!-- Monthly Overview Cards for Current Year -->
        ${this.renderMonthlyOverview()}

        <!-- Yearly Spending Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div id="yearly-spending-chart"></div>
        </div>

        <!-- Yearly Data Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Yearly Performance Summary</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Spending</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Growth %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Purchases</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Vendors</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Parts</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg per Purchase</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${years.map(year => {
                  const yearData = this.companyAnalysis.yearlyData[year];
                  const avgPerPurchase = yearData.totalPurchases > 0 ? yearData.totalSpending / yearData.totalPurchases : 0;
                  const growthClass = (yearData.spendingGrowth || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" data-year="${year}">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">${year}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(yearData.totalSpending)}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${growthClass}">
                        ${yearData.spendingGrowth ? (yearData.spendingGrowth >= 0 ? '+' : '') + yearData.spendingGrowth.toFixed(1) + '%' : '—'}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${yearData.totalPurchases.toLocaleString()}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${yearData.vendorCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${yearData.partCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(avgPerPurchase)}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderMonthlyAnalysis() {
    const currentYear = new Date().getFullYear();
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (!currentYearData) {
      return `
        <div class="text-center py-12">
          <p class="text-gray-500 dark:text-gray-400">No data available for ${currentYear}</p>
        </div>
      `;
    }

    const months = Object.keys(currentYearData.monthlyBreakdown).map(m => parseInt(m)).sort();

    return `
      <div class="space-y-8">
        <!-- Monthly Overview Cards -->
        ${this.renderMonthlyOverview()}

        <!-- Monthly Spending Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div id="monthly-spending-chart"></div>
        </div>

        <!-- Monthly Data Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Monthly Performance - ${currentYear}</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Month</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Spending</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Growth %</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Purchases</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Vendors</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Parts</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg per Purchase</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${months.map(month => {
                  const monthData = currentYearData.monthlyBreakdown[month];
                  const avgPerPurchase = monthData.totalPurchases > 0 ? monthData.totalSpending / monthData.totalPurchases : 0;
                  const growthClass = (monthData.spendingGrowth || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';

                  return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">${monthData.monthName}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(monthData.totalSpending)}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium ${growthClass}">
                        ${monthData.spendingGrowth ? (monthData.spendingGrowth >= 0 ? '+' : '') + monthData.spendingGrowth.toFixed(1) + '%' : '—'}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${monthData.totalPurchases.toLocaleString()}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${monthData.vendorCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${monthData.partCount}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">${this.formatCurrency(avgPerPurchase)}</td>
                    </tr>
                  `;
                }).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderCharts() {
    if (this.selectedView === 'yearly') {
      this.renderYearlyChart();
    } else {
      this.renderMonthlyChart();
    }
  }

  renderYearlyChart() {
    const chartContainer = document.getElementById('yearly-spending-chart');
    if (!chartContainer || !window.ApexCharts) return;

    // Destroy existing chart
    if (this.chartInstances.has('yearly')) {
      this.chartInstances.get('yearly').destroy();
    }

    const years = Object.keys(this.companyAnalysis.yearlyData).map(y => parseInt(y)).sort();
    const spendingData = years.map(year => this.companyAnalysis.yearlyData[year].totalSpending);

    // Calculate yearly price increases and tariff impact
    const priceIncreaseData = years.map((year, index) => {
      if (index === 0) return 0;
      const currentYear = this.companyAnalysis.yearlyData[year];
      const previousYear = this.companyAnalysis.yearlyData[years[index - 1]];

      if (!currentYear || !previousYear || previousYear.totalSpending === 0) return 0;

      const currentAvgCost = currentYear.totalSpending / currentYear.totalPurchases;
      const previousAvgCost = previousYear.totalSpending / previousYear.totalPurchases;

      return ((currentAvgCost - previousAvgCost) / previousAvgCost) * 100;
    });

    const options = {
      series: [{
        name: 'Annual Spending',
        type: 'column',
        data: spendingData
      }, {
        name: 'Price Increase %',
        type: 'line',
        data: priceIncreaseData
      }],
      chart: {
        height: 400,
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        background: 'transparent'
      },
      plotOptions: {
        bar: {
          columnWidth: '45%',
          borderRadius: 4,
          dataLabels: {
            position: 'top'
          }
        }
      },
      stroke: {
        width: [0, 3],
        curve: 'smooth'
      },
      title: {
        text: 'Annual Spending and Price Increase Trends',
        style: {
          fontSize: '18px',
          fontWeight: 600,
          color: '#374151'
        },
        margin: 20
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [0, 1],
        formatter: function(val, opts) {
          if (opts.seriesIndex === 0) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD',
              notation: 'compact'
            }).format(val);
          } else {
            return val.toFixed(1) + '%';
          }
        },
        style: {
          fontSize: '11px',
          fontWeight: 600
        },
        offsetY: -5
      },
      labels: years,
      xaxis: {
        type: 'category',
        title: {
          text: 'Year',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          style: {
            fontSize: '12px',
            fontWeight: 500
          }
        }
      },
      yaxis: [{
        title: {
          text: 'Spending (CAD)',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD',
              notation: 'compact'
            }).format(val);
          },
          style: {
            fontSize: '11px'
          }
        }
      }, {
        opposite: true,
        title: {
          text: 'Price Increase %',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(1) + '%';
          },
          style: {
            fontSize: '11px'
          }
        }
      }],
      tooltip: {
        shared: true,
        intersect: false,
        y: [{
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD'
            }).format(val);
          }
        }, {
          formatter: function(val) {
            return 'Price Increase: ' + val.toFixed(1) + '%';
          }
        }]
      },
      colors: ['#3B82F6', '#10B981'],
      fill: {
        opacity: [0.9, 1],
        gradient: {
          shade: 'light',
          type: 'vertical',
          shadeIntensity: 0.1,
          gradientToColors: ['#60A5FA', '#34D399'],
          inverseColors: false,
          opacityFrom: 0.9,
          opacityTo: 0.7,
          stops: [0, 100]
        }
      },
      grid: {
        borderColor: '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '12px',
        fontWeight: 500,
        markers: {
          width: 8,
          height: 8,
          radius: 2
        }
      }
    };

    const chart = new ApexCharts(chartContainer, options);
    chart.render();
    this.chartInstances.set('yearly', chart);
  }

  renderMonthlyChart() {
    const chartContainer = document.getElementById('monthly-spending-chart');
    if (!chartContainer || !window.ApexCharts) return;

    // Destroy existing chart
    if (this.chartInstances.has('monthly')) {
      this.chartInstances.get('monthly').destroy();
    }

    const currentYear = new Date().getFullYear();
    const currentYearData = this.companyAnalysis.yearlyData[currentYear];

    if (!currentYearData) return;

    const months = Object.keys(currentYearData.monthlyBreakdown).map(m => parseInt(m)).sort();
    const labels = months.map(month => this.getMonthName(month));
    const spendingData = months.map(month => currentYearData.monthlyBreakdown[month].totalSpending);

    // Calculate monthly price increases and tariff impact
    const priceIncreaseData = months.map((month, index) => {
      if (index === 0) return 0;
      const currentMonth = currentYearData.monthlyBreakdown[month];
      const previousMonth = currentYearData.monthlyBreakdown[months[index - 1]];

      if (!currentMonth || !previousMonth || previousMonth.totalSpending === 0) return 0;

      const currentAvgCost = currentMonth.totalSpending / currentMonth.totalPurchases;
      const previousAvgCost = previousMonth.totalSpending / previousMonth.totalPurchases;

      return ((currentAvgCost - previousAvgCost) / previousAvgCost) * 100;
    });

    const tariffImpactData = months.map((month, index) => {
      if (index === 0) return 0;
      const currentMonth = currentYearData.monthlyBreakdown[month];
      const previousMonth = currentYearData.monthlyBreakdown[months[index - 1]];

      return this.calculateTariffImpact(currentMonth, previousMonth);
    });

    const options = {
      series: [{
        name: 'Monthly Spending',
        type: 'column',
        data: spendingData
      }, {
        name: 'Price Increase %',
        type: 'line',
        data: priceIncreaseData
      }, {
        name: 'Tariff Impact %',
        type: 'line',
        data: tariffImpactData
      }],
      chart: {
        height: 400,
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false
          }
        },
        background: 'transparent'
      },
      plotOptions: {
        bar: {
          columnWidth: '35%',
          borderRadius: 4,
          dataLabels: {
            position: 'top'
          }
        }
      },
      stroke: {
        width: [0, 3, 3],
        curve: 'smooth'
      },
      title: {
        text: `Monthly Spending, Price Increases & Tariff Impact - ${currentYear}`,
        style: {
          fontSize: '18px',
          fontWeight: 600,
          color: '#374151'
        },
        margin: 20
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [1, 2],
        formatter: function(val, opts) {
          if (opts.seriesIndex === 1) {
            return val.toFixed(1) + '%';
          } else if (opts.seriesIndex === 2) {
            return 'T:' + val.toFixed(1) + '%';
          }
          return '';
        },
        style: {
          fontSize: '10px',
          fontWeight: 600
        },
        offsetY: -5
      },
      labels: labels,
      xaxis: {
        type: 'category',
        title: {
          text: 'Month',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          style: {
            fontSize: '11px',
            fontWeight: 500
          },
          rotate: -45
        }
      },
      yaxis: [{
        title: {
          text: 'Spending (CAD)',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD',
              notation: 'compact'
            }).format(val);
          },
          style: {
            fontSize: '11px'
          }
        }
      }, {
        opposite: true,
        title: {
          text: 'Price Increase & Tariff Impact %',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(1) + '%';
          },
          style: {
            fontSize: '11px'
          }
        }
      }],
      tooltip: {
        shared: true,
        intersect: false,
        y: [{
          formatter: function(val) {
            return new Intl.NumberFormat('en-CA', {
              style: 'currency',
              currency: 'CAD'
            }).format(val);
          }
        }, {
          formatter: function(val) {
            return 'Price Increase: ' + val.toFixed(1) + '%';
          }
        }, {
          formatter: function(val) {
            return 'Tariff Impact: ' + val.toFixed(1) + '%';
          }
        }]
      },
      colors: ['#3B82F6', '#EF4444', '#F59E0B'],
      fill: {
        opacity: [0.9, 1],
        gradient: {
          shade: 'light',
          type: 'vertical',
          shadeIntensity: 0.1,
          gradientToColors: ['#60A5FA', '#F87171', '#FBBF24'],
          inverseColors: false,
          opacityFrom: 0.9,
          opacityTo: 0.7,
          stops: [0, 100]
        }
      },
      grid: {
        borderColor: '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '12px',
        fontWeight: 500,
        markers: {
          width: 8,
          height: 8,
          radius: 2
        }
      }
    };

    const chart = new ApexCharts(chartContainer, options);
    chart.render();
    this.chartInstances.set('monthly', chart);
  }

  setupEventListeners() {
    // View toggle buttons
    const yearlyViewBtn = this.container.querySelector('#yearly-view');
    const monthlyViewBtn = this.container.querySelector('#monthly-view');

    if (yearlyViewBtn) {
      yearlyViewBtn.addEventListener('click', () => {
        if (this.selectedView !== 'yearly') {
          this.selectedView = 'yearly';
          this.render();
        }
      });
    }

    if (monthlyViewBtn) {
      monthlyViewBtn.addEventListener('click', () => {
        if (this.selectedView !== 'monthly') {
          this.selectedView = 'monthly';
          this.render();
        }
      });
    }

    // Year row click handlers for drilling down
    const yearRows = this.container.querySelectorAll('[data-year]');
    yearRows.forEach(row => {
      row.addEventListener('click', () => {
        const year = parseInt(row.getAttribute('data-year'));
        this.showYearDetails(year);
      });
    });
  }

  showYearDetails(year) {
    const yearData = this.companyAnalysis.yearlyData[year];
    if (!yearData) return;

    // Switch to monthly view and update the year
    this.selectedView = 'monthly';

    // For now, just switch to monthly view
    // In the future, we could add year selection functionality
    this.render();
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    // For executive dashboard, search is not applicable
    // Could be used to filter years or months in the future
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processCompanyAnalysis();
      this.render();
    } else {
      this.renderNoDataState();
    }
  }

  renderNoDataState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-12">
        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Market Data Available</h3>
        <p class="text-gray-500 dark:text-gray-400 text-center max-w-md">
          No purchasing data is available for market analysis. Please ensure price data has been loaded and try refreshing.
        </p>
      </div>
    `;
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  destroy() {
    // Clean up ApexCharts instances
    this.chartInstances.forEach((chart) => {
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
    });
    this.chartInstances.clear();

    if (this.container) {
      this.container.innerHTML = '';
    }
  }

  // External control methods (called by main price tracker)
  updateSearch(searchTerm) {
    this.searchTerm = searchTerm;
    this.currentPage = 1;
    this.applyFilters();
    this.calculateTotalPages();
    this.render();
  }

  updateData(priceData, dateRange) {
    this.priceData = priceData || [];
    this.dateRange = dateRange || {};
    if (this.priceData.length > 0) {
      this.processMarketAnalysis();
      this.applyFilters();
      this.calculateTotalPages();
      this.render();
    } else {
      this.render();
    }
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) return '$0.00';
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  destroy() {
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}
