// Sales Order Analytics component for MRP Dashboard
export class SalesOrderAnalytics {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.salesOrders = [];
    this.weeklyMetrics = [];
    this.pastDueMetrics = {};
    this.isLoading = false;
    this.chart = null; // Single chart instance

    // Display settings
    this.displaySettings = {
      showCurrency: true // Default to showing currency symbols
    };

    // Chart configuration with colors matching the image
    this.chartConfig = {
      theme: 'light', // Always light theme for white background
      colors: {
        missingValue: '#1f2937',        // Dark blue/black (top of stack)
        onHandValue: '#fbbf24',         // Yellow/Gold (bottom of stack)
        weeklyWip: '#10b981',           // Green bars
        wipValue: '#a3e635',            // Light green line
        cumulativeCogs: '#8b5cf6'       // Purple line
      },
      responsive: true
    };
  }

  // Method to update display settings (called by parent component)
  updateDisplaySettings(newSettings) {
    if (newSettings) {
      this.displaySettings = { ...this.displaySettings, ...newSettings };
      console.log("Updated display settings in SalesOrderAnalytics:", this.displaySettings);
    }
  }

  async init() {
    console.log("Initializing Sales Order Analytics component");
    
    // Inherit display settings from parent component if available
    if (this.parentComponent && this.parentComponent.displaySettings) {
      this.displaySettings = { ...this.parentComponent.displaySettings };
      console.log("Inherited display settings from parent:", this.displaySettings);
    }
    
    this.isLoading = true;
    this.render();

    try {
      // Get sales orders data from parent component
      this.salesOrders = this.parentComponent.salesOrders || [];
      console.log(`Processing ${this.salesOrders.length} sales orders for analytics`);
      
      // Get metrics data from parent component (if metrics component exists)
      await this.getMetricsData();
      
      // Process data for chart
      this.processChartData();
      
      this.isLoading = false;
      this.render();
      
      // Initialize chart after render with a small delay to ensure DOM is ready
      setTimeout(async () => {
        try {
          console.log("Starting chart initialization...");
          await this.initializeChart();
          console.log("Chart initialization completed successfully");
        } catch (error) {
          console.error("Chart initialization failed:", error);
          this.showError("Failed to initialize chart: " + error.message);
        }
      }, 100);
    } catch (error) {
      console.error("Error initializing sales order analytics:", error);
      this.isLoading = false;
      this.showError("Failed to initialize analytics: " + error.message);
    }
  }

  async getMetricsData() {
    // Try to get data from metrics component if it exists
    if (this.parentComponent && this.parentComponent.metricsComponent) {
      console.log("Getting data from existing metrics component");
      const metricsComp = this.parentComponent.metricsComponent;
      this.weeklyMetrics = metricsComp.weeklyMetrics || [];
      this.pastDueMetrics = metricsComp.pastDueMetrics || {};
    } else {
      // Calculate metrics data ourselves using same logic as metrics component
      console.log("Calculating metrics data for analytics");
      await this.calculateMetricsData();
    }
  }

  async calculateMetricsData() {
    // Import and use the same logic as SalesOrderMetrics
    const { SalesOrderMetrics } = await import('./sales_order_metric.js');
    const tempMetrics = new SalesOrderMetrics(null, this.parentComponent);
    
    // Initialize without rendering
    tempMetrics.salesOrders = this.salesOrders;
    await tempMetrics.loadInventoryItems();
    await tempMetrics.loadProductionOrders();
    tempMetrics.generateWeeks();
    tempMetrics.calculateWeeklyMetrics();
    
    // Get the calculated data
    this.weeklyMetrics = tempMetrics.weeklyMetrics;
    this.pastDueMetrics = tempMetrics.pastDueMetrics;

    // Store the past due cutoff date for dynamic labeling
    this.pastDueCutoffDate = tempMetrics.pastDueCutoffDate;
  }

  processChartData() {
    console.log("Processing chart data...");
    console.log(`Weekly metrics: ${this.weeklyMetrics.length} weeks`);
    console.log("Past due metrics:", this.pastDueMetrics);
  }

  async initializeChart() {
    console.log("Initializing Chart.js combination chart...");
    
    // Load Chart.js library
    if (typeof Chart === 'undefined') {
      console.log("Chart is undefined, loading Chart.js...");
      await this.loadChartJs();
    } else {
      console.log("Chart.js already available, version:", Chart.version || "unknown");
    }
    
    // Verify Chart is available before creating chart
    if (typeof Chart === 'undefined') {
      console.error("Failed to load Chart.js library");
      throw new Error("Chart.js library not available");
    }
    
    // Create the chart
    this.createCombinationChart();
  }

  async loadChartJs() {
    return new Promise((resolve, reject) => {
      // Check if Chart.js script is already loaded
      if (typeof Chart !== 'undefined') {
        console.log("Chart.js already available globally");
        resolve();
        return;
      }

      console.log("Loading Chart.js UMD library...");

      // Try to load local UMD version first
      const script = document.createElement('script');
      // Use chrome extension relative path
      script.src = chrome.runtime ? chrome.runtime.getURL('kpi/library/chart.umd.min.js') : '/kpi/library/chart.umd.min.js';
      script.type = 'text/javascript';
      
      script.onload = () => {
        console.log("Local Chart.js UMD library loaded");
        // Wait a bit for Chart to be available
        setTimeout(() => {
          if (typeof Chart !== 'undefined') {
            console.log("Chart.js UMD successfully loaded and available");
            resolve();
          } else {
            console.warn("Local Chart.js UMD loaded but Chart not available, trying CDN...");
            this.loadChartJsFromCDN().then(resolve).catch(reject);
          }
        }, 100);
      };
      
      script.onerror = () => {
        console.warn("Failed to load local Chart.js UMD, trying CDN...");
        this.loadChartJsFromCDN().then(resolve).catch(reject);
      };
      
      document.head.appendChild(script);
    });
  }

  async loadChartJsFromCDN() {
    return new Promise((resolve, reject) => {
      // Remove any existing Chart.js scripts to avoid conflicts
      const existingScripts = document.querySelectorAll('script[src*="chart"]');
      existingScripts.forEach(script => {
        if (script.src.includes('chart')) {
          script.remove();
        }
      });

      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
      script.type = 'text/javascript';
      
      script.onload = () => {
        console.log("CDN Chart.js UMD library loaded");
        // Wait a bit for Chart to be available
        setTimeout(() => {
          if (typeof Chart !== 'undefined') {
            console.log("Chart.js UMD successfully loaded from CDN");
            resolve();
          } else {
            reject(new Error("Chart.js UMD loaded from CDN but Chart constructor not available"));
          }
        }, 200);
      };
      
      script.onerror = () => {
        console.error("Failed to load Chart.js UMD from CDN");
        reject(new Error("Failed to load Chart.js UMD from CDN"));
      };
      
      document.head.appendChild(script);
    });
  }

  createCombinationChart() {
    console.log("Creating Chart.js combination chart...");
    console.log("Chart available:", typeof Chart !== 'undefined');
    
    const chartContainer = document.getElementById('analytics-chart-container');
    if (!chartContainer) {
      console.error("Chart container not found");
      return;
    }

    // Prepare data for the chart (this also sets this.chartData for scaling)
    const chartData = this.prepareChartData();
    
    if (!chartData.categories.length) {
      console.warn("No chart data available");
      chartContainer.innerHTML = `
        <div class="flex items-center justify-center h-96 text-gray-500 dark:text-gray-400">
          <div class="text-center">
            <p class="text-lg">No data available for chart</p>
            <p class="text-sm mt-2">Load sales orders to view analytics</p>
          </div>
        </div>
      `;
      return;
    }

    // Clear the container and create canvas
    chartContainer.innerHTML = '<canvas id="analytics-chart" style="background: white;"></canvas>';
    const canvas = document.getElementById('analytics-chart');
    if (!canvas) {
      console.error("Failed to create canvas element");
      return;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error("Failed to get 2D context from canvas");
      return;
    }
    
    console.log("Canvas and context ready, chart data:", {
      categories: chartData.categories.length,
      datasets: Object.keys(chartData).filter(k => k !== 'categories').length
    });

    // Log synchronized scale values
    console.log("Synchronized Y-Axis Scale:", {
      min: this.getChartMinValue(),
      max: this.getChartMaxValue()
    });

    // Destroy existing chart if it exists
    if (this.chart) {
      this.chart.destroy();
    }

    // Create Chart.js configuration
    const config = {
      type: 'bar',
      data: {
        labels: chartData.categories,
        datasets: [
          {
            label: 'On Hand Value',
            data: chartData.onHandValue,
            backgroundColor: this.chartConfig.colors.onHandValue,
            borderColor: this.chartConfig.colors.onHandValue,
            borderWidth: 1,
            stack: 'Stack 1',
            yAxisID: 'y'
          },
          {
            label: 'Missing Value', 
            data: chartData.missingValue,
            backgroundColor: this.chartConfig.colors.missingValue,
            borderColor: this.chartConfig.colors.missingValue,
            borderWidth: 1,
            stack: 'Stack 1',
            yAxisID: 'y'
          },
          {
            label: 'Weekly WIP',
            data: chartData.weeklyWip,
            backgroundColor: this.chartConfig.colors.weeklyWip,
            borderColor: this.chartConfig.colors.weeklyWip,
            borderWidth: 1,
            stack: 'Stack 2',
            yAxisID: 'y'
          },
          {
            label: 'WIP Value',
            data: chartData.wipValue,
            type: 'line',
            backgroundColor: this.chartConfig.colors.wipValue,
            borderColor: this.chartConfig.colors.wipValue,
            borderWidth: 3,
            fill: false,
            tension: 0.4,
            yAxisID: 'y',
            pointBackgroundColor: this.chartConfig.colors.wipValue,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 5
          },
          {
            label: 'Total Cumulative COGs',
            data: chartData.cumulativeCogs,
            type: 'line',
            backgroundColor: this.chartConfig.colors.cumulativeCogs,
            borderColor: this.chartConfig.colors.cumulativeCogs,
            borderWidth: 3,
            fill: false,
            tension: 0.4,
            yAxisID: 'y1',
            pointBackgroundColor: this.chartConfig.colors.cumulativeCogs,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 5
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        backgroundColor: '#ffffff',
        plugins: {
          title: {
            display: true,
            text: 'Production WIP & SCM',
            font: {
              size: 16,
              weight: 'bold'
            },
            color: '#374151'
          },
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              color: '#374151',
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            titleColor: '#374151',
            bodyColor: '#374151',
            borderColor: '#e5e7eb',
            borderWidth: 1,
            callbacks: {
              label: (context) => {
                const label = context.dataset.label || '';
                const value = this.formatCurrency(context.parsed.y);
                return `${label}: ${value}`;
              }
            }
          }
        },
                scales: {
          x: {
            stacked: true,
            grid: {
              display: true,
              color: '#f3f4f6'
            },
            ticks: {
              color: '#374151',
              font: {
                size: 11
              },
              maxRotation: 45
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            stacked: true,
            title: {
              display: true,
              text: 'Values (CAD)',
              color: '#374151',
              font: {
                size: 12,
                weight: 'bold'
              }
            },
            grid: {
              display: true,
              color: '#f3f4f6'
            },
            ticks: {
              color: '#374151',
              callback: (value) => this.formatCurrencyValue(value)
            },
            // Synchronized scale configuration
            min: this.getChartMinValue(),
            max: this.getChartMaxValue()
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: 'Values (CAD)',
              color: '#374151',
              font: {
                size: 12,
                weight: 'bold'
              }
            },
            grid: {
              drawOnChartArea: false
            },
            ticks: {
              color: '#374151',
              callback: (value) => {
                // Scale the value to fit 0-1.5M range
                const maxLeftValue = this.getChartMaxValue();
                const rightAxisValue = (value / maxLeftValue) * 1500000;
                return this.formatCurrencyValue(rightAxisValue);
              }
            },
            // Right axis scale configuration - same scale as left axis but different labels
            min: this.getChartMinValue(),
            max: this.getChartMaxValue()
          }
        },
        interaction: {
          mode: 'index',
          intersect: false
        }
      }
    };

    // Create new chart with error handling
    try {
      console.log("Creating Chart instance...");
      console.log("Chart constructor available:", typeof Chart);
      this.chart = new Chart(ctx, config);
      console.log("Chart.js chart created successfully");
    } catch (error) {
      console.error("Error creating Chart.js chart:", error);
      chartContainer.innerHTML = `
        <div class="flex items-center justify-center h-96 text-red-500">
          <div class="text-center">
            <p class="text-lg">Failed to create chart</p>
            <p class="text-sm mt-2">Error: ${error.message}</p>
          </div>
        </div>
      `;
      return;
    }
  }

  prepareChartData() {
    const categories = [];
    const missingValue = [];
    const weeklyWip = [];
    const onHandValue = [];
    const wipValue = [];
    const cumulativeCogs = [];

    // Add Past Due data first with dynamic label
    if (this.pastDueMetrics && this.pastDueMetrics.ordersCount > 0) {
      const pastDueLabel = this.getPastDueLabel();
      categories.push(pastDueLabel);
      missingValue.push(Number(this.pastDueMetrics.missingValue) || 0);
      weeklyWip.push(Number(this.pastDueMetrics.weeklyWip) || 0);
      onHandValue.push(Number(this.pastDueMetrics.onHandValue) || 0);
      wipValue.push(Number(this.pastDueMetrics.wipValue) || 0);
      cumulativeCogs.push(Number(this.pastDueMetrics.projectCogs) || 0);
    }

    // Add weekly data with proper formatting - limit to 16 weeks
    this.weeklyMetrics.slice(0, 16).forEach(weekMetric => {
      const weekLabel = this.formatWeekLabel(weekMetric.week);
      categories.push(weekLabel);
      missingValue.push(Number(weekMetric.missingValue) || 0);
      weeklyWip.push(Number(weekMetric.weeklyWip) || 0);
      onHandValue.push(Number(weekMetric.onHandValue) || 0);
      wipValue.push(Number(weekMetric.wipValue) || 0);
      cumulativeCogs.push(Number(weekMetric.cumulativeCogs) || 0);
    });

    // Store data for synchronized scaling
    this.chartData = {
      categories,
      missingValue,
      weeklyWip,
      onHandValue,
      wipValue,
      cumulativeCogs
    };

    // Debug log the data values
    console.log('Chart Data Debug:', {
      categories: categories.length,
      missingValue: {
        values: missingValue.slice(0, 5),
        max: Math.max(...missingValue),
        min: Math.min(...missingValue)
      },
      weeklyWip: {
        values: weeklyWip.slice(0, 5),
        max: Math.max(...weeklyWip),
        min: Math.min(...weeklyWip)
      },
      onHandValue: {
        values: onHandValue.slice(0, 5),
        max: Math.max(...onHandValue),
        min: Math.min(...onHandValue)
      },
      wipValue: {
        values: wipValue.slice(0, 5),
        max: Math.max(...wipValue),
        min: Math.min(...wipValue)
      },
      cumulativeCogs: {
        values: cumulativeCogs.slice(0, 5),
        max: Math.max(...cumulativeCogs),
        min: Math.min(...cumulativeCogs)
      }
    });

    return this.chartData;
  }

  getChartMinValue() {
    if (!this.chartData) return 0;
    
    const allValues = [
      ...this.chartData.missingValue,
      ...this.chartData.weeklyWip,
      ...this.chartData.onHandValue,
      ...this.chartData.wipValue,
      ...this.chartData.cumulativeCogs
    ];
    
    const minValue = Math.min(...allValues);
    // Add 10% padding below minimum
    return Math.max(0, minValue * 0.9);
  }

  getChartMaxValue() {
    if (!this.chartData) return 1000;

    // For stacked bars, we need to consider the sum of stacked values
    const stackedBarMaxes = [];
    for (let i = 0; i < this.chartData.categories.length; i++) {
      const stackedValue = (this.chartData.onHandValue[i] || 0) + (this.chartData.missingValue[i] || 0);
      stackedBarMaxes.push(stackedValue);
    }

    const allValues = [
      ...stackedBarMaxes,  // Maximum of stacked bars
      ...this.chartData.weeklyWip,
      ...this.chartData.wipValue,
      ...this.chartData.cumulativeCogs
    ];

    const maxValue = Math.max(...allValues);
    // Add 15% padding above maximum for better visualization
    return maxValue * 1.15;
  }

  formatWeekLabel(week) {
    if (!week) return 'Unknown Week';

    const startDate = new Date(week.startDate);
    const endDate = new Date(week.endDate);

    const formatDate = (date) => {
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const day = date.getDate();
      return `${month} ${day}`;
    };

    return `Week ${week.weekNumber} (${formatDate(startDate)} - ${formatDate(endDate)})`;
  }

  getPastDueLabel() {
    // Get the past due cutoff date from metrics component or stored value
    let cutoffDate = null;

    if (this.pastDueCutoffDate) {
      cutoffDate = this.pastDueCutoffDate;
    } else if (this.parentComponent && this.parentComponent.metricsComponent && this.parentComponent.metricsComponent.pastDueCutoffDate) {
      cutoffDate = this.parentComponent.metricsComponent.pastDueCutoffDate;
    } else {
      // Fallback to today's date
      cutoffDate = new Date();
    }

    const formatDate = (date) => {
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const day = date.getDate();
      const year = date.getFullYear();
      return `${month} ${day}, ${year}`;
    };

    return `Past Due (Before ${formatDate(cutoffDate)})`;
  }

  formatCurrencyValue(value) {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(0) + 'K';
    }
    return value.toFixed(0);
  }

  refresh() {
    console.log("Refreshing Sales Order Analytics");
    
    // Destroy existing chart before refresh
    this.cleanup();
    
    // Re-inherit display settings from parent component
    if (this.parentComponent && this.parentComponent.displaySettings) {
      this.displaySettings = { ...this.parentComponent.displaySettings };
      console.log("Refreshed display settings from parent:", this.displaySettings);
    }
    
    this.init();
  }

  cleanup() {
    // Destroy existing chart to prevent memory leaks
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
      console.log("Chart destroyed for cleanup");
    }
  }

  render() {
    if (!this.container) {
      console.error("No container available for Sales Order Analytics");
      return;
    }

    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading analytics data...</p>
      </div>
    `;
  }

  renderContent() {
    if (this.salesOrders.length === 0) {
      this.renderEmptyState();
      return;
    }

    this.container.innerHTML = `
      <div class="space-y-6">
        <!-- Main Analytics Chart -->
        <div class="bg-white rounded-lg shadow">
          <div class="p-6 bg-white">
            <div id="analytics-chart-container" class="w-full bg-white" style="height: 650px;"></div>
          </div>
        </div>

        <!-- Chart Legend and Information -->
        <div class="bg-white rounded-lg shadow p-6">
          <h4 class="text-md font-medium text-gray-900 mb-4">Chart Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 text-sm">
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background-color: ${this.chartConfig.colors.missingValue}"></div>
              <span class="text-gray-700">Missing Value - Materials not available (stacked on top)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background-color: ${this.chartConfig.colors.onHandValue}"></div>
              <span class="text-gray-700">On Hand Value - Available inventory (stacked bottom)</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 rounded mr-2" style="background-color: ${this.chartConfig.colors.weeklyWip}"></div>
              <span class="text-gray-700">Weekly WIP - Work in progress value</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-1 mr-2" style="background-color: ${this.chartConfig.colors.wipValue}"></div>
              <span class="text-gray-700">WIP Value (Line) - Total work in progress</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-1 mr-2" style="background-color: ${this.chartConfig.colors.cumulativeCogs}"></div>
              <span class="text-gray-700">Cumulative COGs (Line) - Cost accumulation</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderEmptyState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="text-gray-600 dark:text-gray-400">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <p class="text-center text-lg font-medium mb-2">No sales order data available</p>
          <p class="text-center text-sm">Load sales orders to view analytics and charts</p>
        </div>
      </div>
    `;
  }

  exportToCSV() {
    console.log('CSV export functionality will be implemented here');
    
    if (!this.weeklyMetrics.length && !this.pastDueMetrics.ordersCount) {
      this.showError('No data available to export');
      return;
    }

    try {
      // Prepare CSV headers
      const headers = [
        'Week',
        'Missing Value',
        'Weekly WIP',
        'On Hand Value', 
        'WIP Value',
        'Total Cumulative COGs'
      ];

      // Prepare CSV rows
      const rows = [];
      
      // Add Past Due row if exists
      if (this.pastDueMetrics.ordersCount > 0) {
        rows.push([
          'Past Due',
          this.pastDueMetrics.missingValue || 0,
          this.pastDueMetrics.weeklyWip || 0,
          this.pastDueMetrics.onHandValue || 0,
          this.pastDueMetrics.wipValue || 0,
          this.pastDueMetrics.projectCogs || 0
        ]);
      }

      // Add weekly data - limit to 16 weeks
      this.weeklyMetrics.slice(0, 16).forEach(weekMetric => {
        rows.push([
          `Week ${weekMetric.week.weekNumber}`,
          weekMetric.missingValue || 0,
          weekMetric.weeklyWip || 0,
          weekMetric.onHandValue || 0,
          weekMetric.wipValue || 0,
          weekMetric.cumulativeCogs || 0
        ]);
      });

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(field => `"${field}"`).join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_order_analytics_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.showSuccess('Analytics data exported to CSV successfully!');
    } catch (error) {
      console.error('Error exporting analytics to CSV:', error);
      this.showError('Failed to export analytics data: ' + error.message);
    }
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return this.displaySettings.showCurrency ? '$0.00' : '0.00';
    }
    
    if (this.displaySettings.showCurrency) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'CAD',
        minimumFractionDigits: 2
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    }
  }

  showError(message) {
    console.error("Sales Order Analytics Error:", message);
    
    if (this.container) {
      this.container.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }

  showSuccess(message) {
    console.log("Sales Order Analytics Success:", message);
    
    // Create temporary success message
    const successElement = document.createElement('div');
    successElement.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
    successElement.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <span class="block sm:inline">${message}</span>
        </div>
        <div class="ml-auto pl-3">
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-green-500 hover:text-green-700">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(successElement);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (successElement.parentElement) {
        successElement.parentElement.removeChild(successElement);
      }
    }, 3000);
  }
} 